package org.scars.exam.modules.sys.user.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.scars.exam.ability.shiro.jwt.JwtUtils;
import org.scars.exam.core.api.ApiError;
import org.scars.exam.core.api.dto.PagingReqDTO;
import org.scars.exam.core.enums.CommonState;
import org.scars.exam.core.exception.ServiceException;
import org.scars.exam.core.utils.BeanMapper;
import org.scars.exam.core.utils.passwd.PassHandler;
import org.scars.exam.core.utils.passwd.PassInfo;
import org.scars.exam.modules.sys.user.dto.SysUserDTO;
import org.scars.exam.modules.sys.user.dto.request.SysUserSaveReqDTO;
import org.scars.exam.modules.sys.user.dto.response.SysUserLoginDTO;
import org.scars.exam.modules.sys.user.dto.UserNameCheckReqDTO;
import org.scars.exam.modules.sys.user.dto.UserNameConflictRespDTO;
import org.scars.exam.modules.sys.user.dto.BatchUserSaveReqDTO;
import org.scars.exam.modules.sys.user.dto.BatchUserSaveRespDTO;
import org.scars.exam.modules.sys.user.entity.SysUser;
import org.scars.exam.modules.sys.user.mapper.SysUserMapper;
import org.scars.exam.modules.sys.user.service.SysUserRoleService;
import org.scars.exam.modules.sys.user.service.SysUserService;
import org.scars.exam.modules.user.UserUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
* <p>
* 语言设置 服务实现类
* </p>
*
* <AUTHOR>
* @since 2020-04-13 16:57
*/
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService {

    @Autowired
    private SysUserRoleService sysUserRoleService;


    @Override
    public IPage<SysUserDTO> paging(PagingReqDTO<SysUserDTO> reqDTO) {

        //创建分页对象
        IPage<SysUser> query = new Page<>(reqDTO.getCurrent(), reqDTO.getSize());

        //查询条件
        QueryWrapper<SysUser> wrapper = new QueryWrapper<>();

        SysUserDTO params = reqDTO.getParams();

        if(params!=null){
            if(!StringUtils.isBlank(params.getUserName())){
                wrapper.lambda().like(SysUser::getUserName, params.getUserName());
            }

            if(!StringUtils.isBlank(params.getRealName())){
                wrapper.lambda().like(SysUser::getRealName, params.getRealName());
            }
        }

        //获得数据
        IPage<SysUser> page = this.page(query, wrapper);
        //转换结果
        IPage<SysUserDTO> pageData = JSON.parseObject(JSON.toJSONString(page), new TypeReference<Page<SysUserDTO>>(){});
        return pageData;
     }

    @Override
    public SysUserLoginDTO login(String userName, String password) {

        QueryWrapper<SysUser> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(SysUser::getUserName, userName);

        SysUser user = this.getOne(wrapper, false);
        if(user == null){
            throw new ServiceException(ApiError.ERROR_90010002);
        }

        // 被禁用
        if(user.getState().equals(CommonState.ABNORMAL)){
            throw new ServiceException(ApiError.ERROR_90010005);
        }

        boolean check = PassHandler.checkPass(password,user.getSalt(), user.getPassword());
        if(!check){
            throw new ServiceException(ApiError.ERROR_90010002);
        }

        return this.setToken(user);
    }

    @Override
    public SysUserLoginDTO token(String token) {

        // 获得会话
        String username = JwtUtils.getUsername(token);

        // 校验结果
        boolean check = JwtUtils.verify(token, username);

        if(!check){
            throw new ServiceException(ApiError.ERROR_90010002);
        }

        QueryWrapper<SysUser> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(SysUser::getUserName, username);

        SysUser user = this.getOne(wrapper, false);
        if(user == null){
            throw new ServiceException(ApiError.ERROR_10010002);
        }

        // 被禁用
        if(user.getState().equals(CommonState.ABNORMAL)){
            throw new ServiceException(ApiError.ERROR_90010005);
        }

        return this.setToken(user);
    }

    @Override
    public void logout(String token) {

        // 仅退出当前会话
        SecurityUtils.getSubject().logout();
    }

    @Override
    public void update(SysUserDTO reqDTO) {


       String pass = reqDTO.getPassword();
       if(!StringUtils.isBlank(pass)){
           PassInfo passInfo = PassHandler.buildPassword(pass);
           SysUser user = this.getById(UserUtils.getUserId());
           user.setPassword(passInfo.getPassword());
           user.setSalt(passInfo.getSalt());
           this.updateById(user);
       }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(SysUserSaveReqDTO reqDTO) {

        List<String> roles = reqDTO.getRoles();

        if(CollectionUtils.isEmpty(roles)){
            throw new ServiceException(ApiError.ERROR_90010003);
        }

        // 保存基本信息
        SysUser user = new SysUser();
        BeanMapper.copy(reqDTO, user);

        // 添加模式
        if(StringUtils.isBlank(user.getId())){
            user.setId(IdWorker.getIdStr());

            // 新增用户时检查用户名是否重复
            if (!StringUtils.isBlank(user.getUserName())) {
                QueryWrapper<SysUser> wrapper = new QueryWrapper<>();
                wrapper.lambda().eq(SysUser::getUserName, user.getUserName());
                int count = this.count(wrapper);
                if (count > 0) {
                    throw new ServiceException(1, "用户名已存在，请换一个用户名！");
                }
            }
        } else {
            // 编辑用户时检查用户名是否与其他用户重复
            if (!StringUtils.isBlank(user.getUserName())) {
                QueryWrapper<SysUser> wrapper = new QueryWrapper<>();
                wrapper.lambda().eq(SysUser::getUserName, user.getUserName())
                        .ne(SysUser::getId, user.getId());
                int count = this.count(wrapper);
                if (count > 0) {
                    throw new ServiceException(1, "用户名已存在，请换一个用户名！");
                }
            }
        }

        // 修改密码
        if(!StringUtils.isBlank(reqDTO.getPassword())){
            PassInfo pass = PassHandler.buildPassword(reqDTO.getPassword());
            user.setPassword(pass.getPassword());
            user.setSalt(pass.getSalt());
        }

        // 保存角色信息
        String roleIds = sysUserRoleService.saveRoles(user.getId(), roles);
        user.setRoleIds(roleIds);
        this.saveOrUpdate(user);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SysUserLoginDTO reg(SysUserDTO reqDTO) {

        QueryWrapper<SysUser> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(SysUser::getUserName, reqDTO.getUserName());

        int count = this.count(wrapper);

        if(count > 0){
            throw new ServiceException(1, "用户名已存在，换一个吧！");
        }


        // 保存用户
        SysUser user = new SysUser();
        user.setId(IdWorker.getIdStr());
        user.setUserName(reqDTO.getUserName());
        user.setRealName(reqDTO.getRealName());
        PassInfo passInfo = PassHandler.buildPassword(reqDTO.getPassword());
        user.setPassword(passInfo.getPassword());
        user.setSalt(passInfo.getSalt());

        // 保存角色
        List<String> roles = new ArrayList<>();
        roles.add("student");
        String roleIds = sysUserRoleService.saveRoles(user.getId(), roles);
        user.setRoleIds(roleIds);
        this.save(user);

        return this.setToken(user);
    }

    @Override
    public SysUserLoginDTO quickReg(SysUserDTO reqDTO) {

        QueryWrapper<SysUser> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(SysUser::getUserName, reqDTO.getUserName());
        wrapper.last(" LIMIT 1 ");
        SysUser user = this.getOne(wrapper);
        if(user!=null){
            return this.setToken(user);
        }

        return this.reg(reqDTO);
    }

    @Override
    public void resetPassword(String userId, String newPassword) {
        if (StringUtils.isBlank(userId) || StringUtils.isBlank(newPassword)) {
            throw new ServiceException(ApiError.ERROR_90010001);
        }

        SysUser user = this.getById(userId);
        if (user == null) {
            throw new ServiceException(ApiError.ERROR_10010002);
        }

        // 生成新密码
        PassInfo passInfo = PassHandler.buildPassword(newPassword);
        user.setPassword(passInfo.getPassword());
        user.setSalt(passInfo.getSalt());

        this.updateById(user);
    }


    /**
     * 保存会话信息
     * @param user
     * @return
     */
    private SysUserLoginDTO setToken(SysUser user){

        SysUserLoginDTO respDTO = new SysUserLoginDTO();
        BeanMapper.copy(user, respDTO);

        // 生成Token
        String token = JwtUtils.sign(user.getUserName());
        respDTO.setToken(token);

        // 填充角色
        List<String> roles = sysUserRoleService.listRoles(user.getId());
        respDTO.setRoles(roles);

        return respDTO;
    }

    @Override
    public UserNameConflictRespDTO checkUsernameConflicts(UserNameCheckReqDTO reqDTO) {
        UserNameConflictRespDTO respDTO = new UserNameConflictRespDTO();
        List<String> userNames = reqDTO.getUserNames();
        String excludeUserId = reqDTO.getExcludeUserId();

        if (CollectionUtils.isEmpty(userNames)) {
            respDTO.setHasConflict(false);
            respDTO.setConflictUsers(new ArrayList<>());
            respDTO.setAvailableUserNames(new ArrayList<>());
            return respDTO;
        }

        // 查询已存在的用户
        QueryWrapper<SysUser> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(SysUser::getUserName, userNames);
        if (!StringUtils.isBlank(excludeUserId)) {
            wrapper.lambda().ne(SysUser::getId, excludeUserId);
        }
        List<SysUser> existingUsers = this.list(wrapper);

        // 构建冲突用户信息
        List<UserNameConflictRespDTO.ConflictUserInfo> conflictUsers = new ArrayList<>();
        Set<String> conflictUserNames = existingUsers.stream()
                .map(SysUser::getUserName)
                .collect(Collectors.toSet());

        for (SysUser user : existingUsers) {
            UserNameConflictRespDTO.ConflictUserInfo conflictInfo = new UserNameConflictRespDTO.ConflictUserInfo();
            conflictInfo.setUserId(user.getId());
            conflictInfo.setUserName(user.getUserName());
            conflictInfo.setRealName(user.getRealName());
            conflictInfo.setState(user.getState());

            // 生成建议的用户名
            List<String> suggestions = generateSuggestedUserNames(user.getUserName());
            conflictInfo.setSuggestedUserNames(suggestions);

            conflictUsers.add(conflictInfo);
        }

        // 获取可用的用户名
        List<String> availableUserNames = userNames.stream()
                .filter(userName -> !conflictUserNames.contains(userName))
                .collect(Collectors.toList());

        respDTO.setConflictUsers(conflictUsers);
        respDTO.setAvailableUserNames(availableUserNames);
        respDTO.setHasConflict(!conflictUsers.isEmpty());

        return respDTO;
    }

    /**
     * 生成建议的用户名
     */
    private List<String> generateSuggestedUserNames(String originalUserName) {
        List<String> suggestions = new ArrayList<>();

        // 生成数字后缀的建议
        for (int i = 1; i <= 5; i++) {
            String suggestion = originalUserName + i;

            // 检查建议的用户名是否可用
            QueryWrapper<SysUser> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(SysUser::getUserName, suggestion);
            int count = this.count(wrapper);

            if (count == 0) {
                suggestions.add(suggestion);
            }
        }

        return suggestions;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BatchUserSaveRespDTO batchSaveUsers(BatchUserSaveReqDTO reqDTO) {
        BatchUserSaveRespDTO respDTO = new BatchUserSaveRespDTO();
        List<SysUserSaveReqDTO> users = reqDTO.getUsers();

        if (CollectionUtils.isEmpty(users)) {
            respDTO.setTotalCount(0);
            respDTO.setSuccessCount(0);
            respDTO.setFailedCount(0);
            respDTO.setSkippedCount(0);
            respDTO.setIsFullSuccess(true);
            respDTO.setSuccessUsers(new ArrayList<>());
            respDTO.setFailedUsers(new ArrayList<>());
            respDTO.setSkippedUsers(new ArrayList<>());
            return respDTO;
        }

        // 初始化响应数据
        respDTO.setTotalCount(users.size());
        List<BatchUserSaveRespDTO.SavedUserInfo> successUsers = new ArrayList<>();
        List<BatchUserSaveRespDTO.FailedUserInfo> failedUsers = new ArrayList<>();
        List<BatchUserSaveRespDTO.SkippedUserInfo> skippedUsers = new ArrayList<>();

        // 如果不是强制模式，先检查用户名冲突
        if (!Boolean.TRUE.equals(reqDTO.getForceMode())) {
            List<String> userNames = users.stream()
                    .map(SysUserSaveReqDTO::getUserName)
                    .filter(userName -> !StringUtils.isBlank(userName))
                    .collect(Collectors.toList());

            UserNameCheckReqDTO checkReq = new UserNameCheckReqDTO();
            checkReq.setUserNames(userNames);
            UserNameConflictRespDTO conflictResp = checkUsernameConflicts(checkReq);

            if (conflictResp.getHasConflict()) {
                // 根据冲突策略处理
                if (reqDTO.getConflictStrategy() == BatchUserSaveReqDTO.ConflictStrategy.ABORT) {
                    throw new ServiceException(1, "存在用户名冲突，批量操作已终止");
                }

                // 处理冲突用户
                Set<String> conflictUserNames = conflictResp.getConflictUsers().stream()
                        .map(UserNameConflictRespDTO.ConflictUserInfo::getUserName)
                        .collect(Collectors.toSet());

                users = users.stream().filter(user -> {
                    if (conflictUserNames.contains(user.getUserName())) {
                        // 添加到跳过列表
                        BatchUserSaveRespDTO.SkippedUserInfo skippedInfo = new BatchUserSaveRespDTO.SkippedUserInfo();
                        skippedInfo.setUserName(user.getUserName());
                        skippedInfo.setRealName(user.getRealName());
                        skippedInfo.setReason("用户名已存在");

                        // 获取建议的用户名
                        conflictResp.getConflictUsers().stream()
                                .filter(conflict -> conflict.getUserName().equals(user.getUserName()))
                                .findFirst()
                                .ifPresent(conflict -> skippedInfo.setSuggestedUserNames(conflict.getSuggestedUserNames()));

                        skippedUsers.add(skippedInfo);
                        return false;
                    }
                    return true;
                }).collect(Collectors.toList());
            }
        }

        // 批量保存用户
        for (SysUserSaveReqDTO userReq : users) {
            try {
                // 创建用户对象
                SysUser user = new SysUser();
                BeanMapper.copy(userReq, user);
                user.setId(IdWorker.getIdStr());

                // 处理密码
                if (!StringUtils.isBlank(userReq.getPassword())) {
                    PassInfo pass = PassHandler.buildPassword(userReq.getPassword());
                    user.setPassword(pass.getPassword());
                    user.setSalt(pass.getSalt());
                } else {
                    // 默认密码
                    PassInfo pass = PassHandler.buildPassword("123456");
                    user.setPassword(pass.getPassword());
                    user.setSalt(pass.getSalt());
                }

                // 保存角色信息
                List<String> roles = userReq.getRoles();
                if (CollectionUtils.isEmpty(roles)) {
                    roles = new ArrayList<>();
                    roles.add("student"); // 默认学生角色
                }
                String roleIds = sysUserRoleService.saveRoles(user.getId(), roles);
                user.setRoleIds(roleIds);

                // 保存用户
                this.save(user);

                // 添加到成功列表
                BatchUserSaveRespDTO.SavedUserInfo savedInfo = new BatchUserSaveRespDTO.SavedUserInfo();
                savedInfo.setUserId(user.getId());
                savedInfo.setUserName(user.getUserName());
                savedInfo.setRealName(user.getRealName());
                successUsers.add(savedInfo);

            } catch (Exception e) {
                // 添加到失败列表
                BatchUserSaveRespDTO.FailedUserInfo failedInfo = new BatchUserSaveRespDTO.FailedUserInfo();
                failedInfo.setUserName(userReq.getUserName());
                failedInfo.setRealName(userReq.getRealName());
                failedInfo.setErrorMessage(e.getMessage());
                failedUsers.add(failedInfo);
            }
        }

        // 设置响应数据
        respDTO.setSuccessCount(successUsers.size());
        respDTO.setFailedCount(failedUsers.size());
        respDTO.setSkippedCount(skippedUsers.size());
        respDTO.setSuccessUsers(successUsers);
        respDTO.setFailedUsers(failedUsers);
        respDTO.setSkippedUsers(skippedUsers);
        respDTO.setIsFullSuccess(failedUsers.isEmpty() && skippedUsers.isEmpty());

        return respDTO;
    }
}
