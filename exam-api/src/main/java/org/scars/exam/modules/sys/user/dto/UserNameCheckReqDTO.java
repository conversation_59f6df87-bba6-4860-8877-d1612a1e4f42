package org.scars.exam.modules.sys.user.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 用户名检查请求DTO
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 2025-08-02
 */
@Data
@ApiModel(value = "用户名检查请求", description = "用于检查用户名是否已存在的请求对象")
public class UserNameCheckReqDTO {

    /**
     * 待检查的用户名列表
     */
    @ApiModelProperty(value = "待检查的用户名列表", required = true, example = "[\"zhangsan\", \"lisi\", \"wangwu\"]")
    @NotEmpty(message = "用户名列表不能为空")
    @Size(max = 100, message = "单次检查的用户名数量不能超过100个")
    private List<String> userNames;

    /**
     * 排除的用户ID（用于编辑用户时排除自己）
     */
    @ApiModelProperty(value = "排除的用户ID", example = "1234567890")
    private String excludeUserId;
}
