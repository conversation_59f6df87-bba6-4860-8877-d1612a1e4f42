package org.scars.exam.modules.sys.user.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 批量用户保存响应DTO
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 2025-08-02
 */
@Data
@ApiModel(value = "批量用户保存响应", description = "批量保存用户的响应对象")
public class BatchUserSaveRespDTO {

    /**
     * 总数量
     */
    @ApiModelProperty(value = "总数量")
    private Integer totalCount;

    /**
     * 成功数量
     */
    @ApiModelProperty(value = "成功数量")
    private Integer successCount;

    /**
     * 失败数量
     */
    @ApiModelProperty(value = "失败数量")
    private Integer failedCount;

    /**
     * 跳过数量
     */
    @ApiModelProperty(value = "跳过数量")
    private Integer skippedCount;

    /**
     * 成功保存的用户信息
     */
    @ApiModelProperty(value = "成功保存的用户信息")
    private List<SavedUserInfo> successUsers;

    /**
     * 失败的用户信息
     */
    @ApiModelProperty(value = "失败的用户信息")
    private List<FailedUserInfo> failedUsers;

    /**
     * 跳过的用户信息（用户名冲突）
     */
    @ApiModelProperty(value = "跳过的用户信息")
    private List<SkippedUserInfo> skippedUsers;

    /**
     * 操作是否完全成功
     */
    @ApiModelProperty(value = "操作是否完全成功")
    private Boolean isFullSuccess;

    /**
     * 成功保存的用户信息
     */
    @Data
    @ApiModel(value = "成功保存的用户信息")
    public static class SavedUserInfo {
        @ApiModelProperty(value = "用户ID")
        private String userId;
        
        @ApiModelProperty(value = "用户名")
        private String userName;
        
        @ApiModelProperty(value = "真实姓名")
        private String realName;
    }

    /**
     * 失败的用户信息
     */
    @Data
    @ApiModel(value = "失败的用户信息")
    public static class FailedUserInfo {
        @ApiModelProperty(value = "用户名")
        private String userName;
        
        @ApiModelProperty(value = "真实姓名")
        private String realName;
        
        @ApiModelProperty(value = "失败原因")
        private String errorMessage;
    }

    /**
     * 跳过的用户信息
     */
    @Data
    @ApiModel(value = "跳过的用户信息")
    public static class SkippedUserInfo {
        @ApiModelProperty(value = "用户名")
        private String userName;
        
        @ApiModelProperty(value = "真实姓名")
        private String realName;
        
        @ApiModelProperty(value = "跳过原因")
        private String reason;
        
        @ApiModelProperty(value = "建议的替代用户名")
        private List<String> suggestedUserNames;
    }
}
