package org.scars.exam.modules.sys.user.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 用户名冲突响应DTO
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 2025-08-02
 */
@Data
@ApiModel(value = "用户名冲突响应", description = "用户名冲突检查的响应对象")
public class UserNameConflictRespDTO {

    /**
     * 冲突的用户信息列表
     */
    @ApiModelProperty(value = "冲突的用户信息列表")
    private List<ConflictUserInfo> conflictUsers;

    /**
     * 可用的用户名列表
     */
    @ApiModelProperty(value = "可用的用户名列表")
    private List<String> availableUserNames;

    /**
     * 是否存在冲突
     */
    @ApiModelProperty(value = "是否存在冲突")
    private Boolean hasConflict;

    /**
     * 冲突用户信息
     */
    @Data
    @ApiModel(value = "冲突用户信息", description = "已存在的用户信息")
    public static class ConflictUserInfo {
        
        /**
         * 用户ID
         */
        @ApiModelProperty(value = "用户ID")
        private String userId;
        
        /**
         * 用户名
         */
        @ApiModelProperty(value = "用户名")
        private String userName;
        
        /**
         * 真实姓名
         */
        @ApiModelProperty(value = "真实姓名")
        private String realName;
        
        /**
         * 部门名称
         */
        @ApiModelProperty(value = "部门名称")
        private String departName;
        
        /**
         * 状态
         */
        @ApiModelProperty(value = "状态")
        private Integer state;
        
        /**
         * 建议的替代用户名
         */
        @ApiModelProperty(value = "建议的替代用户名")
        private List<String> suggestedUserNames;
    }
}
