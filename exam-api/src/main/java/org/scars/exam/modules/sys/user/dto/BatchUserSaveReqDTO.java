package org.scars.exam.modules.sys.user.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.scars.exam.modules.sys.user.dto.request.SysUserSaveReqDTO;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 批量用户保存请求DTO
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 2025-08-02
 */
@Data
@ApiModel(value = "批量用户保存请求", description = "批量保存用户的请求对象")
public class BatchUserSaveReqDTO {

    /**
     * 用户列表
     */
    @ApiModelProperty(value = "用户列表", required = true)
    @NotEmpty(message = "用户列表不能为空")
    @Size(max = 500, message = "单次批量保存的用户数量不能超过500个")
    @Valid
    private List<SysUserSaveReqDTO> users;

    /**
     * 是否强制保存（跳过用户名冲突检查）
     */
    @ApiModelProperty(value = "是否强制保存", example = "false")
    private Boolean forceMode = false;

    /**
     * 冲突处理策略
     */
    @ApiModelProperty(value = "冲突处理策略", example = "SKIP")
    private ConflictStrategy conflictStrategy = ConflictStrategy.SKIP;

    /**
     * 冲突处理策略枚举
     */
    public enum ConflictStrategy {
        /**
         * 跳过冲突的用户
         */
        SKIP,
        /**
         * 遇到冲突时终止整个操作
         */
        ABORT,
        /**
         * 自动重命名冲突的用户名
         */
        AUTO_RENAME
    }
}
