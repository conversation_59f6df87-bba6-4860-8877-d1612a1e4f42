package org.scars.exam.modules.sys.user.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.scars.exam.core.api.ApiRest;
import org.scars.exam.core.api.controller.BaseController;
import org.scars.exam.core.api.dto.BaseIdReqDTO;
import org.scars.exam.core.api.dto.BaseIdsReqDTO;
import org.scars.exam.core.api.dto.BaseStateReqDTO;
import org.scars.exam.core.api.dto.PagingReqDTO;
import org.scars.exam.core.utils.BeanMapper;
import org.scars.exam.modules.sys.user.dto.SysUserDTO;
import org.scars.exam.modules.sys.user.dto.request.SysUserLoginReqDTO;
import org.scars.exam.modules.sys.user.dto.request.SysUserResetPasswordReqDTO;
import org.scars.exam.modules.sys.user.dto.request.SysUserSaveReqDTO;
import org.scars.exam.modules.sys.user.dto.response.SysUserLoginDTO;
import org.scars.exam.modules.sys.user.dto.UserNameCheckReqDTO;
import org.scars.exam.modules.sys.user.dto.UserNameConflictRespDTO;
import org.scars.exam.modules.sys.user.dto.BatchUserSaveReqDTO;
import org.scars.exam.modules.sys.user.dto.BatchUserSaveRespDTO;
import org.scars.exam.modules.sys.user.entity.SysUser;
import org.scars.exam.modules.sys.user.service.SysUserService;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * 管理用户控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-13 16:57
 */
@Api(tags = {"管理用户"})
@RestController
@RequestMapping("/exam/api/sys/user")
public class SysUserController extends BaseController {

    @Autowired
    private SysUserService baseService;

    /**
     * 用户登录
     * @return
     */
    @CrossOrigin
    @ApiOperation(value = "用户登录")
    @RequestMapping(value = "/login", method = {RequestMethod.POST})
    public ApiRest<SysUserLoginDTO> login(@RequestBody SysUserLoginReqDTO reqDTO) {
        SysUserLoginDTO respDTO = baseService.login(reqDTO.getUsername(), reqDTO.getPassword());
        return super.success(respDTO);
    }

    /**
     * 用户登录
     * @return
     */
    @CrossOrigin
    @ApiOperation(value = "用户登录")
    @RequestMapping(value = "/logout", method = {RequestMethod.POST})
    public ApiRest logout(HttpServletRequest request) {
        String token = request.getHeader("token");
        System.out.println("+++++当前会话为："+token);
        baseService.logout(token);
        return super.success();
    }

    /**
     * 获取会话
     * @return
     */
    @ApiOperation(value = "获取会话")
    @RequestMapping(value = "/info", method = {RequestMethod.POST})
    public ApiRest info(@RequestParam("token") String token) {
        SysUserLoginDTO respDTO = baseService.token(token);
        return success(respDTO);
    }

    /**
     * 修改用户资料
     * @return
     */
    @ApiOperation(value = "修改用户资料")
    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public ApiRest update(@RequestBody SysUserDTO reqDTO) {
        baseService.update(reqDTO);
        return success();
    }


    /**
     * 保存或修改系统用户
     * @return
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "保存或修改")
    @RequestMapping(value = "/save", method = {RequestMethod.POST})
    public ApiRest save(@RequestBody SysUserSaveReqDTO reqDTO) {
        baseService.save(reqDTO);
        return success();
    }

    /**
     * 查找用户详情
     * @param reqDTO
     * @return
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "查找用户详情")
    @RequestMapping(value = "/detail", method = { RequestMethod.POST})
    public ApiRest<SysUserDTO> detail(@RequestBody BaseIdReqDTO reqDTO) {
        SysUser entity = baseService.getById(reqDTO.getId());
        if (entity == null) {
            return super.failure("用户不存在");
        }

        SysUserDTO dto = new SysUserDTO();
        BeanMapper.copy(entity, dto);
        // 清空密码和盐值，不返回给前端
        dto.setPassword(null);
        dto.setSalt(null);

        return super.success(dto);
    }

    /**
     * 重置用户密码
     * @param reqDTO
     * @return
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "重置用户密码")
    @RequestMapping(value = "/reset-password", method = { RequestMethod.POST})
    public ApiRest resetPassword(@RequestBody SysUserResetPasswordReqDTO reqDTO) {
        baseService.resetPassword(reqDTO.getId(), reqDTO.getPassword());
        return super.success();
    }


    /**
     * 批量删除
     * @param reqDTO
     * @return
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "批量删除")
    @RequestMapping(value = "/delete", method = { RequestMethod.POST})
    public ApiRest edit(@RequestBody BaseIdsReqDTO reqDTO) {
        //根据ID删除
        baseService.removeByIds(reqDTO.getIds());
        return super.success();
    }

    /**
     * 分页查找
     * @param reqDTO
     * @return
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "分页查找")
    @RequestMapping(value = "/paging", method = { RequestMethod.POST})
    public ApiRest<IPage<SysUserDTO>> paging(@RequestBody PagingReqDTO<SysUserDTO> reqDTO) {

        //分页查询并转换
        IPage<SysUserDTO> page = baseService.paging(reqDTO);
        return super.success(page);
    }

    /**
     * 修改状态
     * @param reqDTO
     * @return
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "修改状态")
    @RequestMapping(value = "/state", method = { RequestMethod.POST})
    public ApiRest state(@RequestBody BaseStateReqDTO reqDTO) {

        // 条件
        QueryWrapper<SysUser> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .in(SysUser::getId, reqDTO.getIds())
                .ne(SysUser::getUserName, "admin");


        SysUser record = new SysUser();
        record.setState(reqDTO.getState());
        baseService.update(record, wrapper);

        return super.success();
    }


    /**
     * 保存或修改系统用户
     * @return
     */
    @ApiOperation(value = "学员注册")
    @RequestMapping(value = "/reg", method = {RequestMethod.POST})
    public ApiRest<SysUserLoginDTO> reg(@RequestBody SysUserDTO reqDTO) {
        SysUserLoginDTO respDTO = baseService.reg(reqDTO);
        return success(respDTO);
    }

    /**
     * 快速注册，如果手机号存在则登录，不存在就注册
     * @return
     */
    @ApiOperation(value = "快速注册")
    @RequestMapping(value = "/quick-reg", method = {RequestMethod.POST})
    public ApiRest<SysUserLoginDTO> quick(@RequestBody SysUserDTO reqDTO) {
        SysUserLoginDTO respDTO = baseService.quickReg(reqDTO);
        return success(respDTO);
    }

    /**
     * 检查用户名冲突
     * @param reqDTO 用户名检查请求
     * @return 冲突检查结果
     */
    @ApiOperation(value = "检查用户名冲突")
    @RequestMapping(value = "/check-username-conflicts", method = {RequestMethod.POST})
    public ApiRest<UserNameConflictRespDTO> checkUsernameConflicts(@RequestBody UserNameCheckReqDTO reqDTO) {
        UserNameConflictRespDTO respDTO = baseService.checkUsernameConflicts(reqDTO);
        return success(respDTO);
    }

    /**
     * 批量保存用户
     * @param reqDTO 批量保存请求
     * @return 批量保存结果
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "批量保存用户")
    @RequestMapping(value = "/batch-save", method = {RequestMethod.POST})
    public ApiRest<BatchUserSaveRespDTO> batchSaveUsers(@RequestBody BatchUserSaveReqDTO reqDTO) {
        BatchUserSaveRespDTO respDTO = baseService.batchSaveUsers(reqDTO);
        return success(respDTO);
    }
}
