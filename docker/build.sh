#!/bin/bash

# 考试系统Docker构建脚本

set -e

# 显示帮助信息
show_help() {
    echo "📋 考试系统Docker构建脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -e, --export        构建完成后导出镜像为文件"
    echo "  -h, --help          显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                  # 仅构建镜像"
    echo "  $0 --export         # 构建镜像并导出为文件"
    echo "  $0 -e               # 构建镜像并导出为文件（简写）"
}

# 解析命令行参数
EXPORT_IMAGES=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--export)
            EXPORT_IMAGES=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "❌ 未知选项: $1"
            echo "使用 '$0 --help' 查看帮助"
            exit 1
            ;;
    esac
done

echo "🚀 开始构建考试系统Docker镜像..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

# 进入docker目录
cd "$(dirname "$0")"

echo "📦 构建MySQL容器..."
echo "✅ MySQL使用官方镜像，无需构建"

echo "📦 构建Spring Boot后端容器..."
docker-compose build exam-api

echo "📦 构建Vue.js前端容器..."
docker-compose build exam-vue

echo "📦 构建Nginx反向代理容器..."
docker-compose build nginx

echo "🎉 所有镜像构建完成！"
echo ""
echo "📋 构建的镜像："
docker images | grep -E "(exam|mysql)" | head -10

# 如果启用了导出选项，则导出镜像
if [ "$EXPORT_IMAGES" = true ]; then
    echo ""
    echo "📦 开始导出镜像文件..."

    # 创建导出目录
    EXPORT_DIR="./exports"
    mkdir -p "$EXPORT_DIR"

    # 获取镜像名称和标签
    EXAM_API_IMAGE=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep "exam.*exam-api" | head -1)
    EXAM_VUE_IMAGE=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep "exam.*exam-vue" | head -1)
    NGINX_IMAGE=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep "exam.*nginx" | head -1)

    # 生成时间戳
    TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

    echo "📤 导出后端镜像..."
    if [ ! -z "$EXAM_API_IMAGE" ]; then
        docker save "$EXAM_API_IMAGE" | gzip > "$EXPORT_DIR/exam-api_${TIMESTAMP}.tar.gz"
        echo "✅ 后端镜像已导出: $EXPORT_DIR/exam-api_${TIMESTAMP}.tar.gz"
    else
        echo "⚠️  未找到后端镜像"
    fi

    echo "📤 导出前端镜像..."
    if [ ! -z "$EXAM_VUE_IMAGE" ]; then
        docker save "$EXAM_VUE_IMAGE" | gzip > "$EXPORT_DIR/exam-vue_${TIMESTAMP}.tar.gz"
        echo "✅ 前端镜像已导出: $EXPORT_DIR/exam-vue_${TIMESTAMP}.tar.gz"
    else
        echo "⚠️  未找到前端镜像"
    fi

    echo "📤 导出Nginx镜像..."
    if [ ! -z "$NGINX_IMAGE" ]; then
        docker save "$NGINX_IMAGE" | gzip > "$EXPORT_DIR/nginx_${TIMESTAMP}.tar.gz"
        echo "✅ Nginx镜像已导出: $EXPORT_DIR/nginx_${TIMESTAMP}.tar.gz"
    else
        echo "⚠️  未找到Nginx镜像"
    fi

    # 导出MySQL镜像（如果需要）
    echo "📤 导出MySQL镜像..."
    MYSQL_IMAGE="mysql:8.0"
    docker save "$MYSQL_IMAGE" | gzip > "$EXPORT_DIR/mysql_${TIMESTAMP}.tar.gz"
    echo "✅ MySQL镜像已导出: $EXPORT_DIR/mysql_${TIMESTAMP}.tar.gz"

    echo ""
    echo "🎉 镜像导出完成！"
    echo "📁 导出目录: $EXPORT_DIR"
    echo "📋 导出的文件："
    ls -lh "$EXPORT_DIR"/*_${TIMESTAMP}.tar.gz 2>/dev/null || echo "   (无文件导出)"

    echo ""
    echo "💡 使用导出的镜像："
    echo "   docker load < $EXPORT_DIR/exam-api_${TIMESTAMP}.tar.gz"
    echo "   docker load < $EXPORT_DIR/exam-vue_${TIMESTAMP}.tar.gz"
    echo "   docker load < $EXPORT_DIR/nginx_${TIMESTAMP}.tar.gz"
    echo "   docker load < $EXPORT_DIR/mysql_${TIMESTAMP}.tar.gz"
fi

echo ""
echo "🚀 使用以下命令启动服务："
echo "   ./start.sh"
