<template>

  <div>

    <data-table
      ref="pagingTable"
      :options="options"
      :list-query="listQuery"
      @multi-actions="handleMultiAction"
    >

      <template #filter-content>

        <el-input v-model="listQuery.params.userName" style="width: 200px" placeholder="搜索登录名" class="filter-item" />
        <el-input v-model="listQuery.params.realName" style="width: 200px" placeholder="搜索姓名" class="filter-item" />

        <el-button class="filter-item" type="primary" icon="el-icon-plus" @click="handleAdd">
          添加
        </el-button>

        <el-button class="filter-item" type="success" icon="el-icon-upload2" @click="handleBatchAdd">
          批量添加用户
        </el-button>

      </template>

      <template #data-columns>

        <el-table-column
          type="selection"
          width="55"
        />

        <el-table-column
          align="center"
          label="用户名"
        >
          <template v-slot="scope">
            <el-link type="primary" @click="handleUpdate(scope.row)" :underline="false">
              {{ scope.row.userName }}
            </el-link>
          </template>

        </el-table-column>

        <el-table-column
          align="center"
          label="姓名"
          prop="realName"
        />

        <el-table-column
          align="center"
          label="角色"
          prop="roleIds"
        />

        <el-table-column
          align="center"
          label="创建时间"
          prop="createTime"
        />

        <el-table-column
          align="center"
          label="状态"
        >

          <template v-slot="scope">
            <el-tag :type="scope.row.state === 0 ? 'success' : 'danger'">
              {{ scope.row.state | stateFilter }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          label="操作"
          width="120"
        >
          <template v-slot="scope">
            <div class="action-buttons">
              <el-button
                type="primary"
                size="mini"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
              >
                编辑
              </el-button>
              <el-button
                type="warning"
                size="mini"
                icon="el-icon-key"
                @click="handleResetPasswordDirect(scope.row)"
              >
                重置密码
              </el-button>
            </div>
          </template>
        </el-table-column>

      </template>
    </data-table>

    <el-dialog
      :visible.sync="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :close-on-click-modal="false"
    >

      <el-form
        ref="userForm"
        :model="formData"
        :rules="formRules"
        label-position="left"
        label-width="100px"
      >

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户名" prop="userName">
              <el-input
                v-model="formData.userName"
                :disabled="isEdit"
                placeholder="请输入用户名"
                maxlength="50"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="真实姓名" prop="realName">
              <el-input
                v-model="formData.realName"
                placeholder="请输入真实姓名"
                maxlength="50"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="密码" :prop="isEdit ? '' : 'password'">
              <el-input
                v-model="formData.password"
                :placeholder="isEdit ? '不修改请留空' : '请输入密码'"
                type="password"
                maxlength="50"
                show-password
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="state">
              <el-select v-model="formData.state" placeholder="请选择状态" style="width: 100%">
                <el-option label="正常" :value="0" />
                <el-option label="禁用" :value="1" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="部门" prop="departId">
          <depart-tree-select
            v-model="formData.departId"
            :options="treeData || []"
            :props="defaultProps"
            placeholder="请选择部门"
          />
        </el-form-item>

        <el-form-item label="角色" prop="roles">
          <meet-role v-model="formData.roles" />
        </el-form-item>

        <!-- 编辑模式下显示额外信息 -->
        <div v-if="isEdit && formData.createTime" class="user-info">
          <el-divider content-position="left">用户信息</el-divider>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">创建时间：</span>
                <span class="info-value">{{ formData.createTime }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">更新时间：</span>
                <span class="info-value">{{ formData.updateTime || '暂无' }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="handleSave">
          {{ saveLoading ? '保存中...' : '确 定' }}
        </el-button>
        <el-button v-if="isEdit" type="warning" @click="handleResetPassword">
          重置密码
        </el-button>
      </div>

    </el-dialog>

    <!-- 批量添加用户弹窗 -->
    <el-dialog
      :visible.sync="batchDialogVisible"
      title="批量添加用户"
      width="700px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="batchForm"
        :model="batchFormData"
        label-position="left"
        label-width="100px"
      >
        <el-form-item label="选择部门" prop="departId" :rules="[{ required: true, message: '请选择部门', trigger: 'change' }]">
          <depart-tree-select
            v-model="batchFormData.departId"
            :options="treeData || []"
            :props="defaultProps"
            placeholder="请选择要添加用户的部门"
            @selected="handleBatchDepartSelected"
          />
        </el-form-item>

        <el-form-item label="用户信息" prop="userText" :rules="[{ required: true, message: '请输入用户信息', trigger: 'blur' }]">
          <el-input
            v-model="batchFormData.userText"
            type="textarea"
            :rows="12"
            :disabled="!batchFormData.departId"
            :placeholder="batchTextPlaceholder"
            style="font-family: 'Courier New', monospace; font-size: 13px;"
          />
          <div class="batch-stats">
            <span v-if="batchFormData.userText">已输入 {{ batchUserLines.length }} 行用户信息</span>
            <span class="batch-hint">💡 提示：关闭弹窗后输入内容会保留，直到离开用户管理页面</span>
          </div>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleBatchCancel" :disabled="batchSaveLoading">取 消</el-button>
        <el-button type="primary" :loading="batchSaveLoading" @click="handleBatchSave">
          {{ batchSaveLoading ? `正在保存... (${batchProgress.current}/${batchProgress.total})` : '确 定' }}
        </el-button>
      </div>
    </el-dialog>

  </div>

</template>

<script>
import DataTable from '@/components/DataTable'
import MeetRole from '@/components/MeetRole'
import { saveData as saveUserData, fetchDetail, resetPassword } from '@/api/sys/user/user'
import DepartTreeSelect from '@/components/DepartTreeSelect'
import { fetchTree } from '@/api/sys/depart/depart'

export default {
  name: 'SysUserList',
  components: { DepartTreeSelect, DataTable, MeetRole },
  filters: {

    // 订单状态
    userState(value) {
      const map = {
        '0': '正常',
        '1': '禁用'
      }
      return map[value]
    }
  },
  data() {
    return {

      treeData: [],
      defaultProps: {
        value: 'id',
        label: 'deptName',
        children: 'children'
      },
      dialogVisible: false,
      isEdit: false,
      saveLoading: false,

      // 批量添加相关数据
      batchDialogVisible: false,
      batchSaveLoading: false,
      batchFormData: {
        departId: '',
        userText: ''
      },
      batchProgress: {
        current: 0,
        total: 0
      },

      listQuery: {
        current: 1,
        size: 10,
        params: {
        }
      },

      formData: {
        avatar: '',
        state: 0,
        roles: []
      },

      // 表单验证规则
      formRules: {
        userName: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 50, message: '用户名长度在 3 到 50 个字符', trigger: 'blur' },
          { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
        ],
        realName: [
          { required: true, message: '请输入真实姓名', trigger: 'blur' },
          { min: 2, max: 50, message: '姓名长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, max: 50, message: '密码长度在 6 到 50 个字符', trigger: 'blur' }
        ],
        departId: [
          { required: true, message: '请选择部门', trigger: 'change' }
        ],
        roles: [
          { required: true, message: '请选择角色', trigger: 'change' },
          { type: 'array', min: 1, message: '至少选择一个角色', trigger: 'change' }
        ],
        state: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      },

      options: {
        // 列表请求URL
        listUrl: '/exam/api/sys/user/paging',
        // 启用禁用
        stateUrl: '/exam/api/sys/user/state',
        deleteUrl: '/exam/api/sys/user/delete',
        // 批量操作列表
        multiActions: [
          {
            value: 'enable',
            label: '启用'
          }, {
            value: 'disable',
            label: '禁用'
          },
          {
            value: 'delete',
            label: '删除'
          }
        ]
      }
    }
  },

  computed: {
    dialogTitle() {
      return this.isEdit ? '编辑用户' : '添加用户'
    },

    // 批量添加文本框占位符
    batchTextPlaceholder() {
      if (!this.batchFormData.departId) {
        return '请先选择部门后再输入用户信息'
      }
      return `请按以下格式输入用户信息，每行一个用户：
用户名 真实姓名 [角色]

格式说明：
• 用户名：3-50个字符，只能包含字母、数字、下划线
• 真实姓名：2-50个字符
• 角色：可选，空或0表示学生，1表示管理员，默认为学生
• 默认密码：123456，状态：正常

示例：
zhangsan 张三
lisi 李四 0
wangwu 王五 1
zhaoliu 赵六`
    },

    // 批量用户行数统计
    batchUserLines() {
      if (!this.batchFormData.userText) {
        return []
      }
      return this.batchFormData.userText
        .split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0)
    }
  },

  created() {
    this.fetchDepartmentTree()
  },

  beforeDestroy() {
    // 页面销毁时清理批量添加的临时数据
    this.resetBatchForm()
  },

  methods: {
    // 获取部门树
    fetchDepartmentTree() {
      fetchTree({}).then(response => {
        this.treeData = response.data || []
      }).catch(error => {
        console.error('获取部门树失败:', error)
        this.treeData = []
      })
    },

    // 处理图片上传成功
    handleUploadSuccess(response) {
      // 上传图片赋值
      this.formData.avatar = response.data.url
    },

    // 添加用户
    handleAdd() {
      this.isEdit = false
      this.resetForm()
      // 确保部门树数据已加载
      if (!this.treeData || this.treeData.length === 0) {
        this.fetchDepartmentTree()
      }
      this.dialogVisible = true
    },

    // 编辑用户
    handleUpdate(row) {
      this.isEdit = true
      this.resetForm()
      // 确保部门树数据已加载
      if (!this.treeData || this.treeData.length === 0) {
        this.fetchDepartmentTree()
      }
      this.dialogVisible = true

      // 获取用户详情
      this.fetchUserDetail(row.id)
    },

    // 获取用户详情
    fetchUserDetail(id) {
      fetchDetail(id).then(response => {
        if (response.data) {
          this.formData = response.data

          // 处理角色数据
          if (this.formData.roleIds) {
            this.formData.roles = this.formData.roleIds.split(',')
          } else {
            this.formData.roles = []
          }

          // 清空密码字段
          this.formData.password = null
        }
      }).catch(error => {
        console.error('获取用户详情失败:', error)
        this.$message.error('获取用户详情失败')
      })
    },

    // 部门选择回调
    departSelected(data) {
      this.formData.departId = data.id
    },

    // 保存用户
    handleSave() {
      this.$refs.userForm.validate(valid => {
        if (!valid) {
          return false
        }

        this.saveLoading = true

        // 准备保存数据
        const userData = { ...this.formData }

        // 处理角色数据
        if (Array.isArray(userData.roles) && userData.roles.length > 0) {
          userData.roleIds = userData.roles.join(',')
        }

        // 调用保存API
        saveUserData(userData).then(() => {
          this.$message({
            type: 'success',
            message: this.isEdit ? '用户修改成功!' : '用户添加成功!'
          })
          this.dialogVisible = false
          this.$refs.pagingTable.getList()
        }).catch(error => {
          console.error('保存用户失败:', error)
          this.$message.error('保存失败，请检查表单数据')
        }).finally(() => {
          this.saveLoading = false
        })
      })
    },

    // 重置密码
    handleResetPassword() {
      this.$prompt('请输入新密码', '重置密码', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'password',
        inputPattern: /^.{6,50}$/,
        inputErrorMessage: '密码长度在6-50个字符之间'
      }).then(({ value }) => {
        resetPassword(this.formData.id, value).then(() => {
          this.$message({
            type: 'success',
            message: '密码重置成功'
          })
        }).catch(error => {
          console.error('重置密码失败:', error)
          this.$message.error('重置密码失败')
        })
      }).catch(() => {
        // 用户取消输入
      })
    },

    // 取消操作
    handleCancel() {
      this.dialogVisible = false
      this.resetForm()
    },

    // 重置表单
    resetForm() {
      if (this.$refs.userForm) {
        this.$refs.userForm.resetFields()
      }

      this.formData = {
        avatar: '',
        state: 0,
        roles: []
      }
    },

    // 直接重置密码（从列表操作）
    handleResetPasswordDirect(row) {
      this.$prompt(`请输入用户 "${row.realName}" 的新密码`, '重置密码', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'password',
        inputPattern: /^.{6,50}$/,
        inputErrorMessage: '密码长度在6-50个字符之间'
      }).then(({ value }) => {
        resetPassword(row.id, value).then(() => {
          this.$message({
            type: 'success',
            message: `用户 "${row.realName}" 密码重置成功`
          })
        }).catch(error => {
          console.error('重置密码失败:', error)
          this.$message.error('重置密码失败')
        })
      }).catch(() => {
        // 用户取消输入
      })
    },

    // 批量操作监听
    handleMultiAction(obj) {
      if (obj.opt === 'cancel') {
        this.handleCancelOrder(obj.ids)
      }
    },

    // 批量添加用户
    handleBatchAdd() {
      this.resetBatchForm()
      // 确保部门树数据已加载
      if (!this.treeData || this.treeData.length === 0) {
        this.fetchDepartmentTree()
      }
      this.batchDialogVisible = true
    },

    // 批量部门选择回调
    handleBatchDepartSelected(departId) {
      this.batchFormData.departId = departId
    },

    // 解析批量用户文本
    parseBatchUsers(text) {
      const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 0)
      const users = []
      const errors = []

      for (let i = 0; i < lines.length; i++) {
        const lineNumber = i + 1
        const line = lines[i]
        const parts = line.split(/\s+/)

        if (parts.length < 2 || parts.length > 3) {
          errors.push(`第${lineNumber}行：格式错误，应为"用户名 真实姓名 [角色]"`)
          continue
        }

        const [userName, realName, roleFlag] = parts

        // 验证用户名
        if (!/^[a-zA-Z0-9_]+$/.test(userName) || userName.length < 3 || userName.length > 50) {
          errors.push(`第${lineNumber}行：用户名"${userName}"格式错误，应为3-50个字符，只能包含字母、数字、下划线`)
          continue
        }

        // 验证真实姓名
        if (realName.length < 2 || realName.length > 50) {
          errors.push(`第${lineNumber}行：真实姓名"${realName}"长度错误，应为2-50个字符`)
          continue
        }

        // 处理角色
        let roles = ['student'] // 默认为学生
        if (roleFlag !== undefined) {
          if (roleFlag === '1') {
            roles = ['sa'] // 管理员
          } else if (roleFlag !== '0' && roleFlag !== '') {
            errors.push(`第${lineNumber}行：角色标识"${roleFlag}"错误，应为空、0或1`)
            continue
          }
        }

        users.push({
          userName,
          realName,
          roles,
          password: '123456',
          departId: this.batchFormData.departId,
          state: 0,
          avatar: ''
        })
      }

      return { users, errors }
    },

    // 验证用户名是否重复
    checkDuplicateUserNames(users) {
      const userNames = new Set()
      const duplicates = []

      for (const user of users) {
        if (userNames.has(user.userName)) {
          duplicates.push(user.userName)
        } else {
          userNames.add(user.userName)
        }
      }

      return duplicates
    },

    // 批量保存用户
    async handleBatchSave() {
      // 表单验证
      const valid = await this.$refs.batchForm.validate().catch(() => false)
      if (!valid) {
        return
      }

      // 解析用户数据
      const { users, errors } = this.parseBatchUsers(this.batchFormData.userText)

      if (errors.length > 0) {
        this.$alert(errors.join('\n'), '数据格式错误', {
          type: 'error',
          customClass: 'batch-error-dialog'
        })
        return
      }

      if (users.length === 0) {
        this.$message.warning('请输入至少一个用户信息')
        return
      }

      // 检查重复用户名
      const duplicates = this.checkDuplicateUserNames(users)
      if (duplicates.length > 0) {
        this.$alert(`发现重复的用户名：${duplicates.join(', ')}`, '用户名重复', {
          type: 'error'
        })
        return
      }

      // 确认保存
      const confirmResult = await this.$confirm(
        `确定要批量添加 ${users.length} 个用户吗？`,
        '确认批量添加',
        {
          type: 'warning',
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }
      ).catch(() => false)

      if (!confirmResult) {
        return
      }

      // 开始批量保存
      this.batchSaveLoading = true
      this.batchProgress.current = 0
      this.batchProgress.total = users.length

      const successUsers = []
      const failedUsers = []

      for (let i = 0; i < users.length; i++) {
        this.batchProgress.current = i + 1
        const user = users[i]

        try {
          await saveUserData(user)
          successUsers.push(user.userName)
        } catch (error) {
          console.error(`保存用户 ${user.userName} 失败:`, error)
          failedUsers.push({
            userName: user.userName,
            error: (error.response && error.response.data && error.response.data.message) || error.message || '保存失败'
          })
        }

        // 添加小延迟避免请求过快
        await new Promise(resolve => setTimeout(resolve, 100))
      }

      this.batchSaveLoading = false

      // 显示结果
      const successCount = successUsers.length
      const failedCount = failedUsers.length

      if (failedCount === 0) {
        this.$message({
          type: 'success',
          message: `批量添加成功！共添加 ${successCount} 个用户`,
          duration: 3000
        })
        this.batchDialogVisible = false
        this.resetBatchForm() // 全部成功时才清理表单
        this.$refs.pagingTable.getList()
      } else {
        const failedDetails = failedUsers.map(item => `${item.userName}: ${item.error}`).join('\n')
        this.$alert(
          `批量添加完成！\n成功：${successCount} 个\n失败：${failedCount} 个\n\n失败详情：\n${failedDetails}`,
          '批量添加结果',
          {
            type: failedCount < successCount ? 'warning' : 'error',
            customClass: 'batch-result-dialog'
          }
        )

        if (successCount > 0) {
          this.$refs.pagingTable.getList()
        }
      }
    },

    // 取消批量添加
    handleBatchCancel() {
      this.batchDialogVisible = false
      // 注意：这里不调用 resetBatchForm()，保留用户输入的内容
    },

    // 重置批量表单
    resetBatchForm() {
      if (this.$refs.batchForm) {
        this.$refs.batchForm.resetFields()
      }
      this.batchFormData = {
        departId: '',
        userText: ''
      }
      this.batchProgress = {
        current: 0,
        total: 0
      }
    }
  }
}
</script>

<style scoped>
.user-info {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.info-item {
  margin-bottom: 10px;
  font-size: 14px;
}

.info-label {
  color: #606266;
  font-weight: 500;
}

.info-value {
  color: #303133;
  margin-left: 5px;
}

.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

/* 表单样式优化 */
.el-form-item {
  margin-bottom: 20px;
}

.el-input__count {
  color: #909399;
  font-size: 12px;
}

/* 对话框样式 */
.el-dialog__header {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #ebeef5;
}

.el-dialog__body {
  padding: 20px;
}

.el-dialog__footer {
  padding: 10px 20px 20px;
  border-top: 1px solid #ebeef5;
}

/* 分割线样式 */
.el-divider--horizontal {
  margin: 15px 0;
}

.el-divider__text {
  color: #409eff;
  font-weight: 500;
}

/* 操作按钮容器样式 */
.action-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

/* 操作按钮样式 */
.action-buttons .el-button--mini {
  width: 80px;
  margin: 0;
  font-size: 12px;
  padding: 5px 8px;
}

/* 用户名链接样式 */
.el-link {
  font-weight: 500;
}

.el-link:hover {
  text-decoration: underline !important;
}

/* 状态标签样式 */
.el-tag {
  font-weight: 500;
}

/* 批量添加相关样式 */
.batch-stats {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.batch-hint {
  color: #67c23a;
  font-size: 11px;
}

/* 批量错误对话框样式 */
.batch-error-dialog .el-message-box__message {
  white-space: pre-line;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  max-height: 300px;
  overflow-y: auto;
}

/* 批量结果对话框样式 */
.batch-result-dialog .el-message-box__message {
  white-space: pre-line;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  max-height: 400px;
  overflow-y: auto;
}

/* 批量添加按钮样式 */
.filter-item + .filter-item {
  margin-left: 10px;
}
</style>
