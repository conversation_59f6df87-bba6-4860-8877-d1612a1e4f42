import { post } from '@/utils/request'

export function updateData(data) {
  return post('/exam/api/sys/user/update', data)
}

export function saveData(data) {
  return post('/exam/api/sys/user/save', data)
}

export function userReg(data) {
  return post('/exam/api/sys/user/reg', data)
}

/**
 * 获取用户详情
 * @param {string} id 用户ID
 */
export function fetchDetail(id) {
  return post('/exam/api/sys/user/detail', { id: id })
}

/**
 * 重置用户密码
 * @param {string} id 用户ID
 * @param {string} newPassword 新密码
 */
export function resetPassword(id, newPassword) {
  return post('/exam/api/sys/user/reset-password', { id: id, password: newPassword })
}

/**
 * 检查用户名冲突
 * @param {Array} userNames 用户名列表
 * @param {string} excludeUserId 排除的用户ID（可选）
 */
export function checkUsernameConflicts(userNames, excludeUserId = null) {
  const data = {
    userNames: userNames
  }
  if (excludeUserId) {
    data.excludeUserId = excludeUserId
  }
  return post('/exam/api/sys/user/check-username-conflicts', data)
}

/**
 * 批量保存用户
 * @param {Array} users 用户列表
 * @param {Object} options 选项
 */
export function batchSaveUsers(users, options = {}) {
  const data = {
    users: users,
    forceMode: options.forceMode || false,
    conflictStrategy: options.conflictStrategy || 'SKIP'
  }
  return post('/exam/api/sys/user/batch-save', data)
}
